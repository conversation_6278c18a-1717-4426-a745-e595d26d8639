import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/education_model.dart';
import '../../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../Data/education_source.dart';
import 'education_state.dart';

class EducationCubit extends Cubit<EducationState> {
  EducationCubit() : super(EducationState());

  // Load education data
  Future<void> loadEducation({bool refresh = false}) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final userId = UserDataBaseService.getUserDataId();
      final result = await EducationSource.getEducation(
        refresh: refresh,
        userId: userId.isNotEmpty ? userId : null,
      );

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to load education: $error',
            isSuccess: false,
          ),
        ),
        (educationList) => emit(
          state.copyWith(
            educationList: educationList,
            isLoading: false,
            hasChanges: false,
            isSuccess: true,
            errorMessage: null,
          ),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to load education data: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Start adding new education
  void startAddingNew() {
    emit(
      state.copyWith(
        editingEducation: EducationModel(id: EducationModel.generateTimeIdString()),
        isAddingNew: true,
        errorMessage: null,
      ),
    );
  }

  // Start editing education
  void startEditing(EducationModel education) {
    emit(
      state.copyWith(
        editingEducation: education,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Cancel editing
  void cancelEditing() {
    emit(
      state.copyWith(
        editingEducation: null,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Save education
  Future<void> saveEducation({
    required String? degree,
    required String? institution,
    required String? startDate,
    required String? endDate,
    required String? description,
    required String? location,
    required String? gpa,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'User not authenticated',
            isSuccess: false,
          ),
        );
        return;
      }

      final education = EducationModel(
        id: state.editingEducation?.id ?? EducationModel.generateTimeIdString(),
        degree: degree,
        institution: institution,
        startDate: startDate,
        endDate: endDate,
        description: description,
        location: location,
        gpa: gpa,
      );

      final result = state.isAddingNew
          ? await EducationSource.addEducation(education, userId)
          : await EducationSource.updateEducation(education, userId);

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to save education: $error',
            isSuccess: false,
          ),
        ),
        (success) async {
          await loadEducation(refresh: true);
          emit(
            state.copyWith(
              editingEducation: null,
              isAddingNew: false,
              isLoading: false,
              hasChanges: true,
              isSuccess: true,
              errorMessage: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to save education: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Delete education
  Future<void> deleteEducation(EducationModel education) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'User not authenticated',
            isSuccess: false,
          ),
        );
        return;
      }

      final result = await EducationSource.deleteEducation(education.id!, userId);

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to delete education: $error',
            isSuccess: false,
          ),
        ),
        (success) async {
          await loadEducation(refresh: true);
          emit(
            state.copyWith(
              editingEducation: state.editingEducation?.id == education.id
                  ? null
                  : state.editingEducation,
              isAddingNew: state.editingEducation?.id == education.id
                  ? false
                  : state.isAddingNew,
              isLoading: false,
              hasChanges: true,
              isSuccess: true,
              errorMessage: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to delete education: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Reset success state
  void resetSuccess() {
    emit(state.copyWith(isSuccess: null, errorMessage: null));
  }

  // Clear error
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  // Reset state
  void reset() {
    emit(EducationState());
  }
}
