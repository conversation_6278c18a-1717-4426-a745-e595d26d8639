import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/experience_cubit.dart';
import '../Cubit/experience_state.dart';

class ExperienceEditor extends StatefulWidget {
  const ExperienceEditor({super.key});

  @override
  State<ExperienceEditor> createState() => _ExperienceEditorState();
}

class _ExperienceEditorState extends State<ExperienceEditor> {
  @override
  void initState() {
    super.initState();
    context.read<ExperienceCubit>().loadExperience();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Experience Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ExperienceCubit>().startAddingNew();
                    },
                    child: const Text('Add Experience'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),

              // Content
              Expanded(
                child: BlocConsumer<ExperienceCubit, ExperienceState>(
                  listener: (context, state) {
                    if (state.errorMessage != null &&
                        state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<ExperienceCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.experienceList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    return Center(
                      child: CustomText(
                        text:
                            'Experience section - ${state.experienceList.length} entries',
                        fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
