import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/education_cubit.dart';
import '../Cubit/education_state.dart';

class EducationEditor extends StatefulWidget {
  const EducationEditor({super.key});

  @override
  State<EducationEditor> createState() => _EducationEditorState();
}

class _EducationEditorState extends State<EducationEditor> {
  @override
  void initState() {
    super.initState();
    context.read<EducationCubit>().loadEducation();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Education Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<EducationCubit>().startAddingNew();
                    },
                    child: const Text('Add Education'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),
              
              // Content
              Expanded(
                child: BlocConsumer<EducationCubit, EducationState>(
                  listener: (context, state) {
                    if (state.errorMessage != null && state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<EducationCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.educationList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    return Center(
                      child: CustomText(
                        text: 'Education section - ${state.educationList.length} entries',
                        fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
