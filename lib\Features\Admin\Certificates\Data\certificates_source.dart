import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/models/certificate_model.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:dartz/dartz.dart';

class CertificatesSource {
  static Future<Either<String, bool>> addCertificate(
    CertificateModel certificate,
    String userId,
  ) async {
    try {
      final certificateMap = certificate.toJson();

      final response = await SubabaseServices.addMapToList(
        table: 'portfolio_data',
        fieldName: 'certificates',
        mapToAdd: certificateMap,
        matchColumn: 'userId',
        matchValue: userId,
      );

      if (response.status) {
        await UserDataService.addItemToList(LocalStorageKeys.certificates, certificateMap);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> updateCertificate(
    CertificateModel certificate,
    String userId,
  ) async {
    try {
      final certificateMap = certificate.toJson();
      final currentCertificates = UserDataService.getListData(LocalStorageKeys.certificates);
      final existingCertificate = currentCertificates.firstWhere(
        (c) => c['id'] == certificate.id,
        orElse: () => {},
      );
      if (existingCertificate.isEmpty) {
        return Left('Certificate not found for update');
      }
      final response = await SubabaseServices.updateMapInList(
        table: 'portfolio_data',
        fieldName: 'certificates',
        matchColumn: 'userId',
        matchValue: userId,
        updateByKey: 'id',
        updateByValue: certificate.id ?? '',
        newMapData: certificateMap,
      );

      if (response.status) {
        await UserDataService.updateItemInList(
          LocalStorageKeys.certificates,
          certificate.id!,
          certificateMap,
        );
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> deleteCertificate(
    String id,
    String userId,
  ) async {
    try {
      final response = await SubabaseServices.deleteMapFromList(
        table: 'portfolio_data',
        fieldName: 'certificates',
        matchColumn: 'userId',
        matchValue: userId,
        deleteByKey: 'id',
        deleteByValue: id,
      );
      if (response.status) {
        await UserDataService.deleteItemFromList(LocalStorageKeys.certificates, id);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, List<CertificateModel>>> getCertificates({
    bool? refresh,
    String? userId,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getListData(LocalStorageKeys.certificates);
        if (data.isNotEmpty) {
          final certificates = data.map((certificateData) => CertificateModel.fromJson(certificateData)).toList();
          return Right(certificates);
        }
      }
      
      if (userId != null) {
        final response = await SubabaseServices.get(
          table: 'portfolio_data',
          filter: {'userId': userId},
        );
        
        if (response.status && response.data != null && response.data.isNotEmpty) {
          final portfolioData = response.data[0];
          final certificatesList = portfolioData['certificates'] as List<dynamic>? ?? [];
          final certificates = certificatesList
              .map((certificateData) => CertificateModel.fromJson(certificateData as Map<String, dynamic>))
              .toList();
          
          // Update local cache
          await UserDataService.updateListData(
            LocalStorageKeys.certificates,
            certificatesList.map((e) => e as Map<String, dynamic>).toList(),
          );
          
          return Right(certificates);
        }
      }
      
      return Right([]);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
