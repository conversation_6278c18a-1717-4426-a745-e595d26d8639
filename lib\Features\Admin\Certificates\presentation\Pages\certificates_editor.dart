import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/certificates_cubit.dart';
import '../Cubit/certificates_state.dart';

class CertificatesEditor extends StatefulWidget {
  const CertificatesEditor({super.key});

  @override
  State<CertificatesEditor> createState() => _CertificatesEditorState();
}

class _CertificatesEditorState extends State<CertificatesEditor> {
  @override
  void initState() {
    super.initState();
    context.read<CertificatesCubit>().loadCertificates();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Certificates Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<CertificatesCubit>().startAddingNew();
                    },
                    child: const Text('Add Certificate'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),

              // Content
              Expanded(
                child: BlocConsumer<CertificatesCubit, CertificatesState>(
                  listener: (context, state) {
                    if (state.errorMessage != null &&
                        state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<CertificatesCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.certificatesList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    return Center(
                      child: CustomText(
                        text:
                            'Certificates section - ${state.certificatesList.length} entries',
                        fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
