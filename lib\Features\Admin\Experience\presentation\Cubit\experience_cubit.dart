import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/experience_model.dart';
import '../../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../Data/experience_source.dart';
import 'experience_state.dart';

class ExperienceCubit extends Cubit<ExperienceState> {
  ExperienceCubit() : super(ExperienceState());

  Future<void> loadExperience({bool refresh = false}) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final userId = UserDataBaseService.getUserDataId();
      final result = await ExperienceSource.getExperience(
        refresh: refresh,
        userId: userId.isNotEmpty ? userId : null,
      );

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to load experience: $error', isSuccess: false)),
        (experienceList) => emit(state.copyWith(experienceList: experienceList, isLoading: false, hasChanges: false, isSuccess: true, errorMessage: null)),
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to load experience data: $e', isSuccess: false));
    }
  }

  void startAddingNew() {
    emit(state.copyWith(editingExperience: ExperienceModel(id: ExperienceModel.generateTimeIdString()), isAddingNew: true, errorMessage: null));
  }

  void startEditing(ExperienceModel experience) {
    emit(state.copyWith(editingExperience: experience, isAddingNew: false, errorMessage: null));
  }

  void cancelEditing() {
    emit(state.copyWith(editingExperience: null, isAddingNew: false, errorMessage: null));
  }

  Future<void> saveExperience({
    required String? company,
    required String? position,
    required String? startDate,
    required String? endDate,
    required String? description,
    required String? location,
    required List<String>? technologies,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(state.copyWith(isLoading: false, errorMessage: 'User not authenticated', isSuccess: false));
        return;
      }

      final experience = ExperienceModel(
        id: state.editingExperience?.id ?? ExperienceModel.generateTimeIdString(),
        company: company,
        position: position,
        startDate: startDate,
        endDate: endDate,
        description: description,
        location: location,
        technologies: technologies,
      );

      final result = state.isAddingNew
          ? await ExperienceSource.addExperience(experience, userId)
          : await ExperienceSource.updateExperience(experience, userId);

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to save experience: $error', isSuccess: false)),
        (success) async {
          await loadExperience(refresh: true);
          emit(state.copyWith(editingExperience: null, isAddingNew: false, isLoading: false, hasChanges: true, isSuccess: true, errorMessage: null));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to save experience: $e', isSuccess: false));
    }
  }

  Future<void> deleteExperience(ExperienceModel experience) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(state.copyWith(isLoading: false, errorMessage: 'User not authenticated', isSuccess: false));
        return;
      }

      final result = await ExperienceSource.deleteExperience(experience.id!, userId);

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to delete experience: $error', isSuccess: false)),
        (success) async {
          await loadExperience(refresh: true);
          emit(state.copyWith(
            editingExperience: state.editingExperience?.id == experience.id ? null : state.editingExperience,
            isAddingNew: state.editingExperience?.id == experience.id ? false : state.isAddingNew,
            isLoading: false, hasChanges: true, isSuccess: true, errorMessage: null));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to delete experience: $e', isSuccess: false));
    }
  }

  void resetSuccess() {
    emit(state.copyWith(isSuccess: null, errorMessage: null));
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  void reset() {
    emit(ExperienceState());
  }
}
