import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/certificate_model.dart';
import '../../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../Data/certificates_source.dart';
import 'certificates_state.dart';

class CertificatesCubit extends Cubit<CertificatesState> {
  CertificatesCubit() : super(CertificatesState());

  Future<void> loadCertificates({bool refresh = false}) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final userId = UserDataBaseService.getUserDataId();
      final result = await CertificatesSource.getCertificates(
        refresh: refresh,
        userId: userId.isNotEmpty ? userId : null,
      );

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to load certificates: $error', isSuccess: false)),
        (certificatesList) => emit(state.copyWith(certificatesList: certificatesList, isLoading: false, hasChanges: false, isSuccess: true, errorMessage: null)),
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to load certificates data: $e', isSuccess: false));
    }
  }

  void startAddingNew() {
    emit(state.copyWith(editingCertificate: CertificateModel(id: CertificateModel.generateTimeIdString()), isAddingNew: true, errorMessage: null));
  }

  void startEditing(CertificateModel certificate) {
    emit(state.copyWith(editingCertificate: certificate, isAddingNew: false, errorMessage: null));
  }

  void cancelEditing() {
    emit(state.copyWith(editingCertificate: null, isAddingNew: false, errorMessage: null));
  }

  Future<void> saveCertificate({
    required String? name,
    required String? issuingOrganization,
    required String? issueDate,
    required String? expiryDate,
    required String? credentialId,
    required String? description,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(state.copyWith(isLoading: false, errorMessage: 'User not authenticated', isSuccess: false));
        return;
      }

      final certificate = CertificateModel(
        id: state.editingCertificate?.id ?? CertificateModel.generateTimeIdString(),
        name: name,
        issuingOrganization: issuingOrganization,
        issueDate: issueDate,
        expiryDate: expiryDate,
        credentialId: credentialId,
        description: description,
      );

      final result = state.isAddingNew
          ? await CertificatesSource.addCertificate(certificate, userId)
          : await CertificatesSource.updateCertificate(certificate, userId);

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to save certificate: $error', isSuccess: false)),
        (success) async {
          await loadCertificates(refresh: true);
          emit(state.copyWith(editingCertificate: null, isAddingNew: false, isLoading: false, hasChanges: true, isSuccess: true, errorMessage: null));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to save certificate: $e', isSuccess: false));
    }
  }

  Future<void> deleteCertificate(CertificateModel certificate) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(state.copyWith(isLoading: false, errorMessage: 'User not authenticated', isSuccess: false));
        return;
      }

      final result = await CertificatesSource.deleteCertificate(certificate.id!, userId);

      result.fold(
        (error) => emit(state.copyWith(isLoading: false, errorMessage: 'Failed to delete certificate: $error', isSuccess: false)),
        (success) async {
          await loadCertificates(refresh: true);
          emit(state.copyWith(
            editingCertificate: state.editingCertificate?.id == certificate.id ? null : state.editingCertificate,
            isAddingNew: state.editingCertificate?.id == certificate.id ? false : state.isAddingNew,
            isLoading: false, hasChanges: true, isSuccess: true, errorMessage: null));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: 'Failed to delete certificate: $e', isSuccess: false));
    }
  }

  void resetSuccess() {
    emit(state.copyWith(isSuccess: null, errorMessage: null));
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  void reset() {
    emit(CertificatesState());
  }
}
