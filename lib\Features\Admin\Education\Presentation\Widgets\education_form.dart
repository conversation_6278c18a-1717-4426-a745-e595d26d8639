import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/education_model.dart';
import '../../../../../Core/resources/resources.dart';

class EducationForm extends StatefulWidget {
  final EducationModel? education;
  final Function(EducationModel) onSave;
  final VoidCallback onCancel;

  const EducationForm({
    super.key,
    this.education,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<EducationForm> createState() => _EducationFormState();
}

class _EducationFormState extends State<EducationForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _degreeController;
  late TextEditingController _institutionController;
  late TextEditingController _startDateController;
  late TextEditingController _endDateController;
  late TextEditingController _descriptionController;
  late TextEditingController _locationController;
  late TextEditingController _gpaController;

  @override
  void initState() {
    super.initState();
    _degreeController = TextEditingController(text: widget.education?.degree ?? '');
    _institutionController = TextEditingController(text: widget.education?.institution ?? '');
    _startDateController = TextEditingController(text: widget.education?.startDate ?? '');
    _endDateController = TextEditingController(text: widget.education?.endDate ?? '');
    _descriptionController = TextEditingController(text: widget.education?.description ?? '');
    _locationController = TextEditingController(text: widget.education?.location ?? '');
    _gpaController = TextEditingController(text: widget.education?.gpa ?? '');
  }

  @override
  void dispose() {
    _degreeController.dispose();
    _institutionController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _gpaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: widget.education?.id == null ? 'Add Education' : 'Edit Education',
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: Icon(
                    Icons.close,
                    color: Colors.grey,
                    size: 24.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            
            // Form fields
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildTextField(_degreeController, 'Degree/Program'),
                    SizedBox(height: 15.h),
                    _buildTextField(_institutionController, 'Institution'),
                    SizedBox(height: 15.h),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(_startDateController, 'Start Date'),
                        ),
                        SizedBox(width: 15.w),
                        Expanded(
                          child: _buildTextField(_endDateController, 'End Date'),
                        ),
                      ],
                    ),
                    SizedBox(height: 15.h),
                    _buildTextField(_locationController, 'Location'),
                    SizedBox(height: 15.h),
                    _buildTextField(_gpaController, 'GPA (Optional)'),
                    SizedBox(height: 15.h),
                    _buildTextField(
                      _descriptionController,
                      'Description',
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20.h),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Cancel',
                    onPressed: widget.onCancel,
                    backgroundColor: Colors.grey.withAlpha(50),
                    textColor: Colors.grey,
                  ),
                ),
                SizedBox(width: 15.w),
                Expanded(
                  child: CustomButton(
                    text: 'Save',
                    onPressed: _saveEducation,
                    backgroundColor: AppColors.primary,
                    textColor: Colors.white,
                   
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label, {
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: label,
          fontSize: ResponsiveLayout.getSmallFontSize(context),
          fontWeight: FontWeight.w600,
          color: Colors.grey,
        ),
        SizedBox(height: 5.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 8.h,
            ),
          ),
          validator: (value) {
            if (label.contains('Optional')) return null;
            if (value == null || value.isEmpty) {
              return '$label is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  void _saveEducation() {
    if (_formKey.currentState!.validate()) {
      final education = EducationModel(
        id: widget.education?.id ?? EducationModel.generateTimeIdString(),
        degree: _degreeController.text,
        institution: _institutionController.text,
        startDate: _startDateController.text,
        endDate: _endDateController.text,
        description: _descriptionController.text,
        location: _locationController.text,
        gpa: _gpaController.text,
      );
      widget.onSave(education);
    }
  }
}
